from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from app.config.db_configuration import DatabaseConfig
from app.config.settings import settings

class DatabaseSession:
    _async_engine = None
    _async_session_factory = None

    @classmethod
    async def get_async_engine(cls):
        if cls._async_engine is None:
            db_config = DatabaseConfig(
                db_host=settings.DB_HOST,
                db_name=settings.DB_NAME,
                db_username=settings.DB_USER,
                db_password=settings.DB_PASS
            )

            # Use dedicated async URL for aioodbc
            async_url = db_config.async_sqlalchemy_url

            cls._async_engine = create_async_engine(
                async_url,
                echo=False,
                pool_pre_ping=True,     # ✅ Valid
                pool_recycle=3600,      # ✅ Valid
                pool_size=200,           # ✅ Concurrent connection pool
                max_overflow=30,        # ✅ Additional connections under load
                pool_timeout=30,        # ✅ Connection acquisition timeout
                pool_reset_on_return='commit'  # ✅ Clean session state
            )

        return cls._async_engine

    @classmethod
    async def get_async_session_factory(cls):
        if cls._async_session_factory is None:
            engine = await cls.get_async_engine()
            cls._async_session_factory = async_sessionmaker(
                engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
        return cls._async_session_factory

    @classmethod
    async def get_async_session(cls):
        session_factory = await cls.get_async_session_factory()
        return session_factory()

    @classmethod
    async def close_engine(cls):
        """Close the async engine and all connections"""
        try:
            if cls._async_engine:
                await cls._async_engine.dispose()
                cls._async_engine = None
                cls._async_session_factory = None
                print("✅ MSSQL engine closed successfully")
        except Exception as e:
            print(f"❌ Error closing MSSQL engine: {e}")
            raise
