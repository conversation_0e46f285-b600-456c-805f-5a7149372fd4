
import app.config.settings as settings

class DatabaseConfig:
    def __init__(self, db_host, db_name, db_username, db_password):
        self.server = db_host
        self.database = db_name
        self.username = db_username
        self.password = db_password
        self.driver = "ODBC Driver 18 for SQL Server"

    @property
    def sqlalchemy_url(self):
        driver_escaped = self.driver.replace(" ", "+")
        return (
            f"mssql+pyodbc://{self.username}:{self.password}"
            f"@{self.server}/{self.database}?driver={driver_escaped}"
        )

    @property
    def async_sqlalchemy_url(self):
        """Generate proper async URL for aioodbc driver"""
        from urllib.parse import quote_plus
        driver = quote_plus(self.driver)
        return (
            f"mssql+aioodbc://{self.username}:{self.password}"
            f"@{self.server}/{self.database}?driver={driver}"
        )